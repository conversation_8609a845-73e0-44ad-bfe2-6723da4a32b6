# Spring Boot Application Configuration
spring.application.name=elasticsearch-demo

# Server Configuration
server.port=8080

# Elasticsearch Configuration
spring.elasticsearch.uris=http://localhost:9200
spring.elasticsearch.connection-timeout=10s
spring.elasticsearch.socket-timeout=30s

# Logging Configuration
logging.level.com.example=DEBUG
logging.level.org.springframework.data.elasticsearch=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} - %msg%n

# Actuator Configuration
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized
