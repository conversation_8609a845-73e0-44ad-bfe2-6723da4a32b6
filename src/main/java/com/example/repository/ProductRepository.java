package com.example.repository;

import com.example.model.Product;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.repository.ElasticsearchRepository;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;

/**
 * Repository interface for Product entity
 */
@Repository
public interface ProductRepository extends ElasticsearchRepository<Product, String> {

    /**
     * Find products by name containing the given text (case insensitive)
     */
    List<Product> findByNameContainingIgnoreCase(String name);

    /**
     * Find products by category
     */
    List<Product> findByCategory(String category);

    /**
     * Find products by category with pagination
     */
    Page<Product> findByCategory(String category, Pageable pageable);

    /**
     * Find products by price range
     */
    List<Product> findByPriceBetween(BigDecimal minPrice, BigDecimal maxPrice);

    /**
     * Find products by name or description containing the given text
     */
    List<Product> findByNameContainingIgnoreCaseOrDescriptionContainingIgnoreCase(String name, String description);

    /**
     * Find active products
     */
    List<Product> findByActiveTrue();

    /**
     * Find products with stock quantity greater than specified value
     */
    List<Product> findByStockQuantityGreaterThan(Integer quantity);

    /**
     * Find products by category and active status
     */
    List<Product> findByCategoryAndActive(String category, Boolean active);

    /**
     * Count products by category
     */
    long countByCategory(String category);

    /**
     * Check if product exists by name
     */
    boolean existsByName(String name);
}
